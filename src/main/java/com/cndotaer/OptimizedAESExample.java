package com.cndotaer;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class OptimizedAESExample {

    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/ECB/PKCS5Padding";
    private static final String SECRET_KEY = "TJmKeWOtr0WXPmub"; // 128位密钥

    // 自定义Base62字符集（去掉了+/=等特殊字符）
    private static final String BASE62_CHARS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

    /**
     * 方法1：使用Base64URL无填充编码
     */
    public static String encryptWithBase64URL(String plaintext) throws Exception {
        byte[] encryptedBytes = performAESEncryption(plaintext);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(encryptedBytes);
    }

    public static String decryptFromBase64URL(String encryptedBase64) throws Exception {
        byte[] encryptedBytes = Base64.getUrlDecoder().decode(encryptedBase64);
        return performAESDecryption(encryptedBytes);
    }

    /**
     * 方法2：使用Base62编码（更短）
     */
    public static String encryptWithBase62(String plaintext) throws Exception {
        byte[] encryptedBytes = performAESEncryption(plaintext);
        return encodeBase62(encryptedBytes);
    }

    public static String decryptFromBase62(String encryptedBase62) throws Exception {
        byte[] encryptedBytes = decodeBase62(encryptedBase62);
        return performAESDecryption(encryptedBytes);
    }

    /**
     * 方法3：使用十六进制编码（最紧凑的可读格式）
     */
    public static String encryptWithHex(String plaintext) throws Exception {
        byte[] encryptedBytes = performAESEncryption(plaintext);
        return bytesToHex(encryptedBytes);
    }

    public static String decryptFromHex(String encryptedHex) throws Exception {
        byte[] encryptedBytes = hexToBytes(encryptedHex);
        return performAESDecryption(encryptedBytes);
    }

    /**
     * 执行AES加密
     */
    private static byte[] performAESEncryption(String plaintext) throws Exception {
        SecretKeySpec secretKeySpec = new SecretKeySpec(SECRET_KEY.getBytes(StandardCharsets.UTF_8), ALGORITHM);
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);
        return cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 执行AES解密
     */
    private static String performAESDecryption(byte[] encryptedBytes) throws Exception {
        SecretKeySpec secretKeySpec = new SecretKeySpec(SECRET_KEY.getBytes(StandardCharsets.UTF_8), ALGORITHM);
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);
        byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }

    /**
     * Base62编码
     */
    private static String encodeBase62(byte[] data) {
        StringBuilder result = new StringBuilder();
        java.math.BigInteger num = new java.math.BigInteger(1, data);
        java.math.BigInteger base = java.math.BigInteger.valueOf(62);
        
        while (num.compareTo(java.math.BigInteger.ZERO) > 0) {
            java.math.BigInteger[] divmod = num.divideAndRemainder(base);
            result.insert(0, BASE62_CHARS.charAt(divmod[1].intValue()));
            num = divmod[0];
        }
        
        return result.length() == 0 ? "0" : result.toString();
    }

    /**
     * Base62解码
     */
    private static byte[] decodeBase62(String encoded) {
        java.math.BigInteger result = java.math.BigInteger.ZERO;
        java.math.BigInteger base = java.math.BigInteger.valueOf(62);
        
        for (char c : encoded.toCharArray()) {
            result = result.multiply(base).add(java.math.BigInteger.valueOf(BASE62_CHARS.indexOf(c)));
        }
        
        byte[] bytes = result.toByteArray();
        // 移除可能的前导零字节
        if (bytes.length > 1 && bytes[0] == 0) {
            byte[] temp = new byte[bytes.length - 1];
            System.arraycopy(bytes, 1, temp, 0, temp.length);
            return temp;
        }
        return bytes;
    }

    /**
     * 字节数组转十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    /**
     * 十六进制字符串转字节数组
     */
    private static byte[] hexToBytes(String hex) {
        int len = hex.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                    + Character.digit(hex.charAt(i + 1), 16));
        }
        return data;
    }

    public static void main(String[] args) {
        try {
            String originalText = "210235199912314567";
            System.out.println("原始文本: " + originalText + " (长度: " + originalText.length() + ")");
            System.out.println();

            // 方法1: Base64URL无填充
            String base64URLEncrypted = encryptWithBase64URL(originalText);
            System.out.println("Base64URL加密: " + base64URLEncrypted);
            System.out.println("Base64URL长度: " + base64URLEncrypted.length());
            System.out.println("Base64URL解密: " + decryptFromBase64URL(base64URLEncrypted));
            System.out.println();

            // 方法2: Base62编码
            String base62Encrypted = encryptWithBase62(originalText);
            System.out.println("Base62加密: " + base62Encrypted);
            System.out.println("Base62长度: " + base62Encrypted.length());
            System.out.println("Base62解密: " + decryptFromBase62(base62Encrypted));
            System.out.println();

            // 方法3: 十六进制编码
            String hexEncrypted = encryptWithHex(originalText);
            System.out.println("十六进制加密: " + hexEncrypted);
            System.out.println("十六进制长度: " + hexEncrypted.length());
            System.out.println("十六进制解密: " + decryptFromHex(hexEncrypted));
            System.out.println();

            // 比较长度
            System.out.println("=== 长度比较 ===");
            System.out.println("Base64URL: " + base64URLEncrypted.length() + " 字符");
            System.out.println("Base62: " + base62Encrypted.length() + " 字符");
            System.out.println("十六进制: " + hexEncrypted.length() + " 字符");

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
