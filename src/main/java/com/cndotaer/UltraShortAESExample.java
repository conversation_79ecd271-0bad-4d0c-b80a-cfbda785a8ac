package com.cndotaer;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.math.BigInteger;

public class UltraShortAESExample {

    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/ECB/PKCS5Padding";
    private static final String SECRET_KEY = "TJmKeWOtr0WXPmub"; // 128位密钥

    // 自定义Base85字符集（更高的编码密度）
    private static final String BASE85_CHARS = 
        "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz!#$%&()*+-;<=>?@^_`{|}~";

    /**
     * 使用Base85编码实现最短的加密字符串
     */
    public static String encryptUltraShort(String plaintext) throws Exception {
        byte[] encryptedBytes = performAESEncryption(plaintext);
        return encodeBase85(encryptedBytes);
    }

    public static String decryptUltraShort(String encryptedBase85) throws Exception {
        byte[] encryptedBytes = decodeBase85(encryptedBase85);
        return performAESDecryption(encryptedBytes);
    }

    /**
     * 针对数字字符串的特殊优化版本
     * 先将数字字符串转换为更紧凑的二进制表示，再加密
     */
    public static String encryptNumericOptimized(String numericString) throws Exception {
        // 将数字字符串转换为BigInteger，然后转为字节数组
        BigInteger number = new BigInteger(numericString);
        byte[] compactBytes = number.toByteArray();
        
        // 加密紧凑的字节数组
        SecretKeySpec secretKeySpec = new SecretKeySpec(SECRET_KEY.getBytes(StandardCharsets.UTF_8), ALGORITHM);
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);
        byte[] encryptedBytes = cipher.doFinal(compactBytes);
        
        return encodeBase85(encryptedBytes);
    }

    public static String decryptNumericOptimized(String encryptedBase85) throws Exception {
        byte[] encryptedBytes = decodeBase85(encryptedBase85);
        
        // 解密
        SecretKeySpec secretKeySpec = new SecretKeySpec(SECRET_KEY.getBytes(StandardCharsets.UTF_8), ALGORITHM);
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);
        byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
        
        // 将字节数组转换回数字字符串
        BigInteger number = new BigInteger(decryptedBytes);
        return number.toString();
    }

    /**
     * 执行标准AES加密
     */
    private static byte[] performAESEncryption(String plaintext) throws Exception {
        SecretKeySpec secretKeySpec = new SecretKeySpec(SECRET_KEY.getBytes(StandardCharsets.UTF_8), ALGORITHM);
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);
        return cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 执行标准AES解密
     */
    private static String performAESDecryption(byte[] encryptedBytes) throws Exception {
        SecretKeySpec secretKeySpec = new SecretKeySpec(SECRET_KEY.getBytes(StandardCharsets.UTF_8), ALGORITHM);
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);
        byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }

    /**
     * Base85编码 - 比Base64更紧凑
     */
    private static String encodeBase85(byte[] data) {
        if (data.length == 0) return "";
        
        BigInteger num = new BigInteger(1, data);
        BigInteger base = BigInteger.valueOf(85);
        StringBuilder result = new StringBuilder();
        
        while (num.compareTo(BigInteger.ZERO) > 0) {
            BigInteger[] divmod = num.divideAndRemainder(base);
            result.insert(0, BASE85_CHARS.charAt(divmod[1].intValue()));
            num = divmod[0];
        }
        
        return result.length() == 0 ? String.valueOf(BASE85_CHARS.charAt(0)) : result.toString();
    }

    /**
     * Base85解码
     */
    private static byte[] decodeBase85(String encoded) {
        BigInteger result = BigInteger.ZERO;
        BigInteger base = BigInteger.valueOf(85);
        
        for (char c : encoded.toCharArray()) {
            int index = BASE85_CHARS.indexOf(c);
            if (index == -1) {
                throw new IllegalArgumentException("Invalid Base85 character: " + c);
            }
            result = result.multiply(base).add(BigInteger.valueOf(index));
        }
        
        byte[] bytes = result.toByteArray();
        // 移除可能的前导零字节
        if (bytes.length > 1 && bytes[0] == 0) {
            byte[] temp = new byte[bytes.length - 1];
            System.arraycopy(bytes, 1, temp, 0, temp.length);
            return temp;
        }
        return bytes;
    }

    /**
     * 自定义紧凑编码 - 使用更大的字符集
     */
    private static final String COMPACT_CHARS = 
        "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz!@#$%^&*()_+-=[]{}|;:,.<>?";

    public static String encryptCompact(String plaintext) throws Exception {
        byte[] encryptedBytes = performAESEncryption(plaintext);
        return encodeCompact(encryptedBytes);
    }

    public static String decryptCompact(String encryptedCompact) throws Exception {
        byte[] encryptedBytes = decodeCompact(encryptedCompact);
        return performAESDecryption(encryptedBytes);
    }

    private static String encodeCompact(byte[] data) {
        BigInteger num = new BigInteger(1, data);
        BigInteger base = BigInteger.valueOf(COMPACT_CHARS.length());
        StringBuilder result = new StringBuilder();
        
        while (num.compareTo(BigInteger.ZERO) > 0) {
            BigInteger[] divmod = num.divideAndRemainder(base);
            result.insert(0, COMPACT_CHARS.charAt(divmod[1].intValue()));
            num = divmod[0];
        }
        
        return result.length() == 0 ? String.valueOf(COMPACT_CHARS.charAt(0)) : result.toString();
    }

    private static byte[] decodeCompact(String encoded) {
        BigInteger result = BigInteger.ZERO;
        BigInteger base = BigInteger.valueOf(COMPACT_CHARS.length());
        
        for (char c : encoded.toCharArray()) {
            int index = COMPACT_CHARS.indexOf(c);
            if (index == -1) {
                throw new IllegalArgumentException("Invalid compact character: " + c);
            }
            result = result.multiply(base).add(BigInteger.valueOf(index));
        }
        
        byte[] bytes = result.toByteArray();
        if (bytes.length > 1 && bytes[0] == 0) {
            byte[] temp = new byte[bytes.length - 1];
            System.arraycopy(bytes, 1, temp, 0, temp.length);
            return temp;
        }
        return bytes;
    }

    public static void main(String[] args) {
        try {
            String originalText = "210235199912314567";
            System.out.println("原始文本: " + originalText + " (长度: " + originalText.length() + ")");
            System.out.println();

            // 方法1: Base85编码
            String base85Encrypted = encryptUltraShort(originalText);
            System.out.println("Base85加密: " + base85Encrypted);
            System.out.println("Base85长度: " + base85Encrypted.length());
            System.out.println("Base85解密: " + decryptUltraShort(base85Encrypted));
            System.out.println();

            // 方法2: 数字优化版本
            String numericOptimized = encryptNumericOptimized(originalText);
            System.out.println("数字优化加密: " + numericOptimized);
            System.out.println("数字优化长度: " + numericOptimized.length());
            System.out.println("数字优化解密: " + decryptNumericOptimized(numericOptimized));
            System.out.println();

            // 方法3: 紧凑编码
            String compactEncrypted = encryptCompact(originalText);
            System.out.println("紧凑编码加密: " + compactEncrypted);
            System.out.println("紧凑编码长度: " + compactEncrypted.length());
            System.out.println("紧凑编码解密: " + decryptCompact(compactEncrypted));
            System.out.println();

            // 最终比较
            System.out.println("=== 最终长度比较 ===");
            System.out.println("Base85: " + base85Encrypted.length() + " 字符");
            System.out.println("数字优化: " + numericOptimized.length() + " 字符");
            System.out.println("紧凑编码: " + compactEncrypted.length() + " 字符");

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
